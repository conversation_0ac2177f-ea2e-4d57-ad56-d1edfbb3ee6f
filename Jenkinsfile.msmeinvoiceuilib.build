pipeline {
    agent any

    parameters {
        string(name: '<PERSON><PERSON>LD_BRANCH', defaultValue: 'develop', description: 'Branch to build')
        string(name: 'SUCCESS_EMAILS', defaultValue: '<EMAIL>,<EMAIL>', description: 'Comma-separated list of email IDs for successful builds')
        string(name: 'FAILURE_EMAILS', defaultValue: '<EMAIL>,<EMAIL>', description: 'Comma-separated list of email IDs for failed builds')
    }

   environment {
           GIT_URL = 'ssh://git-codecommit.ap-south-1.amazonaws.com/v1/repos/msme-invoice-ui-lib'
           GIT_BRANCH = "${params.BUILD_BRANCH}"
           SONARQUBE_SERVER = 'teapot-sonar'
           SONAR_PROJECT_KEY = 'msme-invoice-ui-lib'
           AWS_REGION = 'ap-south-1'
       }

    stages {
        stage('Clone Repository') {
            steps {
                script {
                    // Checkout the repository from CodeCommit and capture commit ID
                    git branch: "${GIT_BRANCH}", url: "${GIT_URL}"
                    env.GIT_COMMIT_ID = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
                }
            }
        }

        stage('Check for olk-package Changes') {
            steps {
                script {
                    def changes = sh(script: "git diff --name-only HEAD~1 HEAD", returnStdout: true).trim().split("\n")
                    def relevantChanges = changes.findAll { it.startsWith("olk-package/") }
                    if (relevantChanges.isEmpty()) {
                        currentBuild.result = 'SUCCESS'
                        echo "No changes in olk-package/. Skipping build and publish."
                        skipRemainingStages = true
                    }
                }
            }
        }

        stage('Change Directory to olk-package') {
            when {
                expression { return !binding.hasVariable('skipRemainingStages') }
            }
            steps {
                dir('olk-package') {
                    script {
                        env.PACKAGE_VERSION = sh(script: "node -p \"require('./package.json').version\"", returnStdout: true).trim()
                    }
                }
            }
        }

        // stage('Auto Version Bump') {
        //     when {
        //         expression { return !binding.hasVariable('skipRemainingStages') }
        //     }
        //     steps {
        //         dir('olk-package') {
        //             script {
        //                 sh 'npm version patch --no-git-tag-version'
        //                 env.PACKAGE_VERSION = sh(script: "node -p \"require('./package.json').version\"", returnStdout: true).trim()
        //             }
        //         }
        //     }
        // }

        stage('Install Dependencies') {
            when {
                expression { return !binding.hasVariable('skipRemainingStages') }
            }
            steps {
                dir('olk-package') {
                    sh 'npm install'
                }
            }
        }

        stage('Run Build') {
            when {
                expression { return !binding.hasVariable('skipRemainingStages') }
            }
            steps {
                dir('olk-package') {
                    sh 'npm run build'
                }
            }
        }

        stage('Set up AWS credentials') {
            when {
                expression { return !binding.hasVariable('skipRemainingStages') }
            }
            steps {
                // Ensure AWS CLI is installed and configure AWS credentials
                withCredentials([[$class: 'AmazonWebServicesCredentialsBinding', credentialsId: 'aws_cred']]) {
                    script {
                        sh '''
                            export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
                            export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
                            export AWS_DEFAULT_REGION=$AWS_REGION
                            aws sts get-caller-identity
                        '''
                        def codeartifactAuthToken = sh(
                        script: "aws codeartifact get-authorization-token --domain tgiic --domain-owner 004335400582 --region ap-south-1 --query authorizationToken --output text",
                        returnStdout: true
                        ).trim()
                        env.CODEARTIFACT_AUTH_TOKEN = codeartifactAuthToken
                        //echo "CODEARTIFACT_AUTH_TOKEN: ${env.CODEARTIFACT_AUTH_TOKEN}"
                    }
                }
            }
        }

        stage('Configure NPM and Publish') {
            when {
                expression { return !binding.hasVariable('skipRemainingStages') }
            }
            steps {
                dir('olk-package') {
                    script {
                        sh '''
                            aws codeartifact login --tool npm --domain tgiic --domain-owner 004335400582 --repository msme-platform --region ap-south-1
                            npm publish
                        '''
                    }
                }
            }
        }
    }

    post {
        always {
               script {
                       // Store build duration and Jenkins build URL for notifications
                        env.BUILD_DURATION = currentBuild.durationString.replace(' and counting', '')
                        env.JENKINS_BUILD_URL = env.BUILD_URL
               }
        }
        success {
            echo 'Build was successful!'

            emailext(
                subject: "🎉 Build Successful: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: """
                    <html>
                        <body>
                            <p>🟢 <strong>Great news!</strong> The build completed <strong>successfully</strong>.</p>
                            <ul>
                                <li>🛠️ <strong>Project</strong>: ${env.JOB_NAME}</li>
                                <li>🔢 <strong>Build Number</strong>: ${env.BUILD_NUMBER}</li>
                                <li>🕒 <strong>Duration</strong>: ${env.BUILD_DURATION}</li>
                                <li>🔀 <strong>Commit ID</strong>: ${env.GIT_COMMIT_ID}</li>
                                <li>🌐 <strong>Jenkins Build URL</strong>:
                                    <a href="${env.JENKINS_BUILD_URL}">${env.JENKINS_BUILD_URL}</a>
                                </li>
                            </ul>
                            <p>Everything is in good shape! Keep up the great work, team! 🚀</p>
                        </body>
                    </html>
                """,
                mimeType: 'text/html',
                to: "${params.SUCCESS_EMAILS}"
            )
        }
        failure {
            emailext(
                subject: "❌ Build Failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
                body: """
                    <html>
                        <body>
                            <p>🔴 <strong>The build encountered some issues and <strong>failed</strong>.</p>
                            <ul>
                                <li>🛠️ <strong>Project</strong>: ${env.JOB_NAME}</li>
                                <li>🔢 <strong>Build Number</strong>: ${env.BUILD_NUMBER}</li>
                                <li>🕒 <strong>Duration</strong>: ${env.BUILD_DURATION}</li>
                                <li>🔀 <strong>Commit ID</strong>: ${env.GIT_COMMIT_ID}</li>
                                <li>🌐 <strong>Jenkins Build URL</strong>:
                                    <a href="${env.JENKINS_BUILD_URL}">${env.JENKINS_BUILD_URL}</a>
                                </li>
                            </ul>
                            <p>Please check the Jenkins console log for more details and investigate the issues.</p>
                            <p>Let's resolve this and get back on track! 💪</p>
                        </body>
                    </html>
                """,
                mimeType: 'text/html',
                to: "${params.FAILURE_EMAILS}",
                attachLog: true,
                compressLog: true
            )
        }
    }
}