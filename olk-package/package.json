{"name": "@iris-olk/invoice", "version": "1.1.0", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsc && npm run copy-locales && npm run copy-styles", "watch": "tsc --watch & npm run watch-assets", "copy-locales": "mkdir -p dist/locales && cp -r src/locales/* dist/locales/", "copy-styles": "mkdir -p dist/styles && cp -r src/styles/* dist/styles/", "watch-assets": "nodemon --watch src/locales --watch src/styles --exec 'npm run copy-locales && npm run copy-styles'"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "@types/node": "20.3.1", "axios": "^1.7.9", "cookies-next": "^5.1.0", "js-cookie": "^3.0.5", "next-intl": "^3.26.5", "primereact": "^10.2.1", "react-hook-form": "^7.54.2", "react-pdf": "^9.2.1", "zod": "^3.24.1"}, "peerDependencies": {"next": "^15.1.6", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "typescript": "^5.1.3"}, "license": "MIT"}