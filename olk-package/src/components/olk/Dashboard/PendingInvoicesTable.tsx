"use client";

import { usePara<PERSON>, useSearchPara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "primereact/button";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { getOrgDataField } from "../../../utils/cookies";
import apiCall from '../../../utils/apiCallService';


interface PendingInvoice {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    invoicetotal: string;
    amountpaid: string;
    pendingamount: string;
}
interface Party {
  custid: number | string;
  custname?: string;
}

interface InvoiceApiResponse {
    olkstatus: number;
    invoices: PendingInvoice[];
}

interface ApiResponse {
  olkstatus: number;
  olkresult: [];
}

const PendingInvoicesTable = () => {
    const params = useParams();
    const searchParams = useSearchParams();
    const router = useRouter();
    const [invoices, setInvoices] = useState<PendingInvoice[]>([]);
    const [loading, setLoading] = useState(false);
    const [partyName, setPartyName] = useState<string>("");
    const { OLK_PATH } = useEnvContext();
    const orgCode = Number(getOrgDataField("orgcode"));

    const partyId = params.partyId as string;
    const type = searchParams.get("type") as "receipts" | "payments";
    const inoutflag = type === "receipts" ? 15 : 9;

    useEffect(() => {
        fetchPendingInvoices();
        fetchPartyDetails();
    }, [partyId]);

    const fetchPartyDetails = async () => {
        try {
          const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/parties?orgcode=${orgCode}`);
            
            // const response = await axiosInstance.get(`/parties`, {
            //     baseURL: OLK_PATH,
            //     params: {
            //         orgcode: orgCode,
            //     },
            // });

           const data = response.data;

if (data.olkstatus === 0 && data.olkresult) {
    const parties = data.olkresult as Party[]; // Cast to known type

    const party = parties.find((p) => p.custid.toString() === partyId);
    if (party) {
        setPartyName(party.custname || "Unknown Party");
    }
}
        } catch (error) {
            console.error("Error fetching party details:", error);
            setPartyName("Unknown Party");
        }
    };

    const fetchPendingInvoices = async () => {
        setLoading(true);
        try {
            const response = await apiCall<InvoiceApiResponse>("GET", `${OLK_PATH}/invoice/unpdbyparty?orgcode=${orgCode}&inoutflag=${inoutflag}&custid=${partyId}`);
            const data = response.data as InvoiceApiResponse;
            setInvoices(data.invoices || []);
        } catch (error) {
            setInvoices([]);
        } finally {
            setLoading(false);
        }
    };

    const handleViewInvoice = (invid: number) => {
        router.push(`/dashboard/invoice/sales-and-purchase/view/${invid}`);
    };

    const formatCurrency = (value: string) => {
        return `₹${Number(value).toLocaleString("en-IN", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        })}`;
    };

    const invoiceNumberTemplate = (rowData: PendingInvoice) => {
        return <span className="font-bold">{rowData.invoiceno}</span>;
    };

    const amountTemplate = (
        rowData: PendingInvoice,
        field: keyof PendingInvoice,
        color: string
    ) => {
        const value = rowData[field];
        const safeValue = value?.toString() || "0";
        return (
            <span className={`font-semibold ${color}`}>
                {formatCurrency(safeValue)}
            </span>
        );
    };

    const actionTemplate = (rowData: PendingInvoice) => {
        return (
            <Button
                icon="pi pi-eye"
                text
                onClick={() => handleViewInvoice(rowData.invid)}
                className="text-blue-400"
                tooltip="View Invoice"
                style={{ alignSelf: "flex-end" }}
            />
        );
    };

    return (
        <div className="card">
            <div className="flex justify-content-between align-items-center mb-4">
                <h2 className="text-2xl font-bold">
                    {type === "receipts"
                        ? "Pending Receipts"
                        : "Pending Payments"}
                    {partyName && ` - ${partyName}`}
                </h2>
                
            </div>

            {loading ? (
                <div className="flex justify-content-center">
                    <i
                        className="pi pi-spin pi-spinner"
                        style={{ fontSize: "2rem" }}
                    ></i>
                </div>
            ) : (
                <DataTable
                    value={invoices}
                    rows={50}
                    className="p-datatable-sm"
                    emptyMessage="No pending invoices found"
                    responsiveLayout="stack"
                    breakpoint="960px"
                    showGridlines
                    stripedRows
                    size="small"
                    paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                >
                    <Column
                        field="invoiceno"
                        header="Invoice"
                        body={invoiceNumberTemplate}
                        sortable
                        headerStyle={{ backgroundColor: "#bfdbfe" }}
                    />
                    <Column
                        field="invoicedate"
                        header="Date"
                        sortable
                        headerStyle={{ backgroundColor: "#bfdbfe" }}
                    />
                    <Column
                        field="invoicetotal"
                        header="Total Amount"
                        body={(rowData) =>
                            amountTemplate(rowData, "invoicetotal", "text-900")
                        }
                        sortable
                        headerStyle={{ backgroundColor: "#bfdbfe" }}
                    />
                    <Column
                        field="amountpaid"
                        header="Amount Paid"
                        body={(rowData) => (
                            <span
                                className="font-semibold"
                                style={{ color: "#188926" }}
                            >
                                {formatCurrency(rowData.amountpaid)}
                            </span>
                        )}
                        sortable
                        headerStyle={{ backgroundColor: "#bfdbfe" }}
                    />
                    <Column
                        field="pendingamount"
                        header="Pending Amount"
                        body={(rowData) =>
                            amountTemplate(
                                rowData,

                                "pendingamount",
                                "text-red-500"
                            )
                        }
                        sortable
                        headerStyle={{ backgroundColor: "#bfdbfe" }}
                    />
                    <Column
                        header="Action"
                        body={actionTemplate}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                    />
                </DataTable>
            )}
        </div>
    );
};

export default PendingInvoicesTable;
