"use client";
import React, { useState, useEffect } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { But<PERSON> } from "primereact/button";
import { Card } from "primereact/card";
import { InputText } from "primereact/inputtext";
import { RadioButton } from "primereact/radiobutton";
import { Calendar } from "primereact/calendar";
import { useRouter } from "next/navigation";
import { getOrgDataField } from "../../../utils/cookies";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import "../../../styles/olkcss.scss";
import apiCall from '../../../utils/apiCallService';


// Utility function to format numbers in Indian style with commas
const formatIndianCurrency = (amount: number | string): string => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "₹0.00";

    // Convert to fixed 2 decimal places
    const fixedNum = num.toFixed(2);
    const [integerPart, decimalPart] = fixedNum.split(".");

    // Apply Indian number formatting (lakhs and crores)
    const lastThreeDigits = integerPart.slice(-3);
    const otherDigits = integerPart.slice(0, -3);

    if (otherDigits !== "") {
        const formattedOtherDigits = otherDigits.replace(
            /\B(?=(\d{2})+(?!\d))/g,
            ","
        );
        return `${formattedOtherDigits},${lastThreeDigits}.${decimalPart}`;
    } else {
        return `${lastThreeDigits}.${decimalPart}`;
    }
};

//   CashBillList Component

//   Displays a list of cash bills with filtering and search capabilities.
//   Similar to InvoiceList but specifically for cash bills.

interface CashBill {
    id: number;
    billNo: string;
    billDate: string;
    taxableAmount: string;
    cgstAmount: string;
    sgstAmount: string;
    totalAmount: string;
}

interface ApiCashBill {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    invoicetotal: string;
    nettotal: string;
    amountpaid: string;
}

interface ApiResponse {
    olkstatus: number;
    cashbills: ApiCashBill[];
}

const CashBillList: React.FC = () => {
    const router = useRouter();
    const [cashBills, setCashBills] = useState<CashBill[]>([]);
    const [filteredCashBills, setFilteredCashBills] = useState<CashBill[]>([]);
    const [cashBillType, setCashBillType] = useState<"sales" | "purchase">(
        "sales"
    );
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [globalFilter, setGlobalFilter] = useState<string>("");
    const [dateRange, setDateRange] = useState<Date[] | null>(null);

    const { OLK_PATH } = useEnvContext();
    const orgcode = Number(getOrgDataField("orgcode"));
    const isoString1 = getOrgDataField("yearstart");
        const yearstart = isoString1
            ? new Date(isoString1).toISOString().split("T")[0]
            : "";
        const isoString = getOrgDataField("yearend");
        const yearend = isoString
            ? new Date(isoString).toISOString().split("T")[0]
            : "";

    // Fetch cash bills from API

    useEffect(() => {
        const fetchCashBills = async () => {
            setLoading(true);
            setError(null);
            try {
                // Determine which URL to use based on cash bill type
                const inoutflag = cashBillType === "sales" ? 15 : 9;
                const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/invoice/getbills?orgcode=${orgcode}&inoutflag=${inoutflag}&yearstart=${yearstart}&enddate=${yearend}`);

                // console.log("Cash Bills API Response:", response.data);

                if (
                    response.data?.olkstatus === 0 &&
                    response.data?.cashbills
                ) {
                    const transformedCashBills: CashBill[] =
                        response.data.cashbills.map(
                            (apiCashBill: ApiCashBill) => {
                                // Calculate total GST amount as difference between total and net
                                const totalAmount = parseFloat(
                                    apiCashBill.invoicetotal
                                );
                                const taxableAmount = parseFloat(
                                    apiCashBill.nettotal
                                );
                                const totalGstAmount =
                                    totalAmount - taxableAmount;

                                // Split GST equally between CGST and SGST (for intrastate transactions)
                                const cgstAmount = totalGstAmount / 2;
                                const sgstAmount = totalGstAmount / 2;

                                return {
                                    id: apiCashBill.invid,
                                    billNo: apiCashBill.invoiceno,
                                    billDate: apiCashBill.invoicedate,
                                    taxableAmount: apiCashBill.nettotal,
                                    cgstAmount: cgstAmount.toFixed(2),
                                    sgstAmount: sgstAmount.toFixed(2),
                                    totalAmount: apiCashBill.invoicetotal,
                                };
                            }
                        );

                    setCashBills(transformedCashBills);
                    setFilteredCashBills(transformedCashBills);
                } else {
                    console.warn(
                        "No cash bills found or invalid response structure"
                    );
                    setCashBills([]);
                    setFilteredCashBills([]);
                }
            } catch (err) {
                console.error("Error fetching cash bills:", err);
                setError("Failed to fetch cash bills. Please try again.");
                setCashBills([]);
                setFilteredCashBills([]);
            } finally {
                setLoading(false);
            }
        };

        if (orgcode && OLK_PATH) {
            fetchCashBills();
        }
    }, [orgcode, OLK_PATH, cashBillType]);

    // Apply filters to cash bills

    useEffect(() => {
        let filtered = [...cashBills];

        // Apply global filter (search)
        if (globalFilter) {
            filtered = filtered.filter((cashBill) =>
                Object.values(cashBill).some((value) =>
                    value
                        .toString()
                        .toLowerCase()
                        .includes(globalFilter.toLowerCase())
                )
            );
        }

        // Apply date range filter
        if (
            dateRange &&
            dateRange.length === 2 &&
            dateRange[0] &&
            dateRange[1]
        ) {
            filtered = filtered.filter((cashBill) => {
                const cashBillDate = new Date(cashBill.billDate);
                return (
                    cashBillDate >= dateRange[0] && cashBillDate <= dateRange[1]
                );
            });
        }

        setFilteredCashBills(filtered);
    }, [cashBills, globalFilter, dateRange]);

    // Action button template for viewing cash bills

    const actionBodyTemplate = (rowData: CashBill) => {
        return (
            <div className="flex justify-center items-center w-full">
                <Button
                    icon="pi pi-eye"
                    className="p-button-sm p-button-text !flex !items-center !justify-center !p-2 m-auto"
                    onClick={() => {
                        router.push(
                            `/dashboard/invoice/cashbill/view/${rowData.id}`
                        );
                    }}
                />
            </div>
        );
    };

    // Header template with filters

    const header = (
        <div className="flex flex-column md:flex-row md:justify-content-between md:align-items-center gap-3">
            <div className="flex gap-2">
                <Button
                    type="button"
                    icon="pi pi-filter-slash"
                    label="Clear"
                    outlined
                    onClick={() => {
                        setGlobalFilter("");
                        setDateRange(null);
                    }}
                />
            </div>
            <div className="flex gap-2">
                <span className="p-input-icon-left">
                    <i className="pi pi-search" />
                    <InputText
                        value={globalFilter}
                        onChange={(e) => setGlobalFilter(e.target.value)}
                        placeholder="Search cash bills..."
                    />
                </span>
                <Calendar
                    value={dateRange}
                    onChange={(e) => setDateRange(e.value as Date[])}
                    selectionMode="range"
                    readOnlyInput
                    placeholder="Select Date Range"
                />
            </div>
        </div>
    );

    const handleAddCashBill = () => {
        router.push(`/dashboard/invoice/cashbill?type=${cashBillType}`);
    };

    return (
        <div className="cash-bill-list-container">
            <style>{`
        .cash-bill-list-container {
          padding: 0.1rem;
        }

        .radio-inline {
          display: flex;
          gap: 0.75rem;
          margin-bottom: 1rem;
        }

        /* Mobile-specific styling */
        @media (max-width: 768px) {
          .cash-bill-table {
            display: none; /* Hide DataTable on mobile */
          }

          /* Mobile header styling */
          .cash-bill-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
          }

          .cash-bill-header h2 {
            text-align: center;
            margin-bottom: 0.5rem;
          }

          .cash-bill-header .add-button {
            width: 100%;
            justify-content: center;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            min-height: 44px; /* Minimum touch target */
          }

          .cash-bill-card-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem; /* Increased gap for better separation */
            margin: 0 -0.25rem; /* Negative margin to expand cards */
          }

          .cash-bill-card {
            background: #ffffff;
            border: 1px solid #e2e8f0; /* Softer border color */
            border-radius: 16px; /* Larger rounded corners */
            padding: 1rem; /* Increased padding for better mobile usability */
            min-height: 200px; /* Minimum height for larger card with more fields */
            width: 100%; /* Full width utilization */
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08); /* Stronger shadow for depth */
            transition: transform 0.2s ease, box-shadow 0.2s ease; /* Smooth hover/tap effect */
          }

          .cash-bill-card:hover {
            transform: translateY(-2px); /* Slight lift on hover/tap */
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); /* Enhanced shadow on hover */
          }

          .cash-bill-card div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.875rem; /* Increased spacing between rows */
          }

          .cash-bill-card label {
            font-weight: 600; /* Bold for emphasis */
            color: #1f2937; /* Darker gray for contrast */
            font-size: 1rem; /* Larger font for readability */
            flex-shrink: 0; /* Prevent label shrinking */
          }

          .cash-bill-card span {
            color: #4b5563; /* Softer gray for secondary text */
            font-size: 1rem; /* Larger font for readability */
            text-align: right; /* Right align values */
          }

          .cash-bill-card .bill-no {
            font-size: 1.25rem; /* Larger for bill number */
            font-weight: 700; /* Bold for emphasis */
            color: #6366f1; /* Brand color */
          }

          .cash-bill-card .action {
            justify-content: flex-end;
            margin-bottom: 0; /* Remove bottom margin for action row */
          }

          .cash-bill-card .action .p-button {
            width: 44px; /* Minimum touch target width */
            height: 44px; /* Minimum touch target height */
            min-width: 44px;
            min-height: 44px;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            padding: 0 !important;
            border-radius: 50% !important;
            background-color: #6366f1 !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3) !important;
          }

        

          .cash-bill-card .action .p-button .pi-eye {
            color: white !important;
            font-size: 1.1rem !important;
            line-height: 1 !important;
          }
        }

        /* Desktop-specific styling */
        @media (min-width: 769px) {
          .cash-bill-card-container {
            display: none; /* Hide cards on desktop */
          }

          .cash-bill-table {
            display: block; /* Ensure DataTable is visible */
          }
        }
      `}</style>

            <Card className="p-2">
                <div className="flex justify-content-between align-items-center mb-4 cash-bill-header">
                    <h2 className="text-xl md:text-2xl font-semibold">
                        {`Cash Bills - ${
                            cashBillType === "sales" ? "Sales" : "Purchase"
                        } `}
                    </h2>
                    <div className="flex gap-2">
                        <Button
                            label="Add Cash Bill"
                            icon="pi pi-plus"
                            className="p-button-success add-button"
                            onClick={handleAddCashBill}
                        />
                    </div>
                </div>

                {/* Radio buttons for Sales/Purchase selection */}
                <div className="flex gap-4 mb-4">
                    <div className="flex align-items-center">
                        <RadioButton
                            inputId="sales"
                            name="cashBillType"
                            value="sales"
                            onChange={(e) => setCashBillType(e.value)}
                            checked={cashBillType === "sales"}
                        />
                        <label htmlFor="sales" className="ml-2">
                            Sales
                        </label>
                    </div>
                    <div className="flex align-items-center">
                        <RadioButton
                            inputId="purchase"
                            name="cashBillType"
                            value="purchase"
                            onChange={(e) => setCashBillType(e.value)}
                            checked={cashBillType === "purchase"}
                        />
                        <label htmlFor="purchase" className="ml-2">
                            Purchase
                        </label>
                    </div>
                </div>

                {error && (
                    <div className="p-3 mb-4 bg-red-50 border border-red-200 rounded text-red-700">
                        {error}
                    </div>
                )}

                {/* DataTable for Desktop */}
                <div className="cash-bill-table">
                    <DataTable
                        value={filteredCashBills}
                        paginator
                        rows={15}
                        // rowsPerPageOptions={[5, 10, 15, 25, 50]}
                        loading={loading}
                        header={header}
                        emptyMessage="No cash bills found."
                        className="p-datatable-sm"
                        scrollable
                        scrollHeight="calc(100vh - 100px)"
                        resizableColumns
                        columnResizeMode="fit"
                        showGridlines
                        stripedRows
                        size="small"
                        paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
                        currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
                    >
                        <Column
                            field="billNo"
                            header="Bill No"
                            sortable
                            // style={{ minWidth: "120px" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                            body={(rowData) => (
                                <span className="font-bold">
                                    {rowData.billNo}
                                </span>
                            )}
                        />
                        <Column
                            field="billDate"
                            header="Date"
                            sortable
                            // style={{ minWidth: "120px" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                            body={(rowData) => {
                                const date = new Date(rowData.billDate);
                                const day = date
                                    .getDate()
                                    .toString()
                                    .padStart(2, "0");
                                const month = (date.getMonth() + 1)
                                    .toString()
                                    .padStart(2, "0");
                                const year = date.getFullYear();
                                return `${day}/${month}/${year}`;
                            }}
                        />
                        <Column
                            field="taxableAmount"
                            header="Taxable Amount"
                            sortable
                            // style={{ minWidth: "140px" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                            body={(rowData) =>
                                formatIndianCurrency(rowData.taxableAmount)
                            }
                            className="text-left"
                        />
                        <Column
                            field="cgstAmount"
                            header="CGST"
                            sortable
                            // style={{ minWidth: "100px" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                            body={(rowData) =>
                                formatIndianCurrency(rowData.cgstAmount)
                            }
                            className="text-left"
                        />
                        <Column
                            field="sgstAmount"
                            header="SGST"
                            sortable
                            // style={{ minWidth: "100px" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                            body={(rowData) =>
                                formatIndianCurrency(rowData.sgstAmount)
                            }
                            className="text-left"
                        />
                        <Column
                            field="totalAmount"
                            header="Total Amount"
                            sortable
                            // style={{ minWidth: "140px" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                            body={(rowData) =>
                                formatIndianCurrency(rowData.totalAmount)
                            }
                            className="text-left"
                        />
                        <Column
                            header="Action"
                            body={actionBodyTemplate}
                            // style={{ minWidth: "80px", textAlign: "center" }}
                            headerStyle={{
                                textAlign: "center",
                                backgroundColor: "#bfdbfe",
                            }}
                        />
                    </DataTable>
                </div>

                {/* Card Layout for Mobile */}
                <div className="cash-bill-card-container">
                    {loading ? (
                        <div className="text-center text-gray-500 text-lg">
                            Loading...
                        </div>
                    ) : filteredCashBills.length === 0 ? (
                        <div className="text-center text-gray-500 text-lg">
                            No cash bills found.
                        </div>
                    ) : (
                        filteredCashBills.map((cashBill) => (
                            <div key={cashBill.id} className="cash-bill-card">
                                <div>
                                    <span className="bill-no">
                                        {cashBill.billNo}
                                    </span>
                                              <span className="text-gray-900 text-md" >
                                        {(() => {
                                            const date = new Date(
                                                cashBill.billDate
                                            );
                                            const day = date
                                                .getDate()
                                                .toString()
                                                .padStart(2, "0");
                                            const month = (date.getMonth() + 1)
                                                .toString()
                                                .padStart(2, "0");
                                            const year = date.getFullYear();
                                            return `${day}/${month}/${year}`;
                                        })()}
                                    </span>                     
                                    </div>
                        
                                
                                <div>
                                    <label>Taxable Amount:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            cashBill.taxableAmount
                                        )}
                                    </span>
                                </div>
                                <div>
                                    <label>CGST:</label>
                                        {formatIndianCurrency(
                                            cashBill.cgstAmount
                                        )}
                                     <label>SGST:</label>
                                   
                                        {formatIndianCurrency(
                                            cashBill.sgstAmount
                                        )}
                                  
                                </div>
                                <div>
                                   
                                </div>
                                <div>
                                    <label>Total Amount:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            cashBill.totalAmount
                                        )}
                                    </span>
                                </div>
                                <div className="justify-content-end">
                                    <Button
                                        icon="pi pi-eye"
                                        className="p-button-sm p-button-text"
                                        onClick={() =>
                                            router.push(
                                                `/dashboard/invoice/cashbill/view/${cashBill.id}`
                                            )
                                        }
                                    />
                               
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </Card>
        </div>
    );
};

export default CashBillList;
