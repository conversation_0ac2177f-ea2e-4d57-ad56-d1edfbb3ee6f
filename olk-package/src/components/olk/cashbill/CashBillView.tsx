"use client";
import React, { useState, useEffect } from "react";
import { Card } from "primereact/card";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { ProgressSpinner } from "primereact/progressspinner";
import { Message } from "primereact/message";
import { Panel } from "primereact/panel";
import { Divider } from "primereact/divider";
import { Button } from "primereact/button";
import { AxiosError } from "axios";
import { getOrgDataField } from "../../../utils/cookies";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import { PDFDownloadLink } from "@react-pdf/renderer";
import CashBillPDF from "./CashBillPdf";
import { CashBillCombinedData } from "./CashBillPdf";
import "../../../styles/olkcss.scss";
import apiCall from '../../../utils/apiCallService';


//   CashBillView Component

//   Displays detailed view of a specific cash bill.
//   Takes styling reference from InvoiceView component.

interface CashBillViewProps {
    cashBillId: string;
}

interface CashBillContent {
    gsflag: number;
    gstflag: number;
    gstrate: number;
    quantity: number;
    gstamount: number;
    productcode: number;
    productname: string;
    freeQuantity: number;
    pricePerUnit: number;
    productAmount: number;
    taxableAmount: number;
    discountAmount: number;
}

interface CashBillRecord {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    invnarration: string | null;
    taxflag: number;
    contents: CashBillContent[];
    amountpaid: string;
    invoicetotal: string;
    nettotal: string;
    icflag: number;
    roundoffflag: number;
    lockflag: number;
    discflag: number;
    taxstate: string;
    sourcestate: string;
    orgstategstin: string | null;
    attachment: string | null;
    attachmentcount: number;
    orgcode: number;
    paymentmode: number;
    inoutflag: number;
    invoicetotalword: string;
}

interface ApiResponse {
    invrecord: CashBillRecord;
}

// Define invoice preferences interface
interface InvoicePrefs {
    logo?: string;
    sign?: string;
    autoinvno?: number;
    tandc?: string;
}

// Define orgDetails interfaces
interface orgFields {
    bankdetails: null | string;
    gstin: string;
    orgpan: string;
    orgaddr: string;
    orgpincode: string;
    invoice_preferences?: InvoicePrefs;
}

interface details {
    bisdetails: orgFields;
    olkstatus?: number;
}

interface BisDetails {
  bankdetails: any;
  gstin: string;
  orgpan: string;
  orgaddr: string;
  orgpincode: string;
}



interface OrgBdtResponse {
  olkstatus: number;
  bisdetails: BisDetails;
  
}


const formatIndianCurrency = (amount: number | string): string => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "₹0.00";

    // Convert to fixed 2 decimal places
    const fixedNum = num.toFixed(2);
    const [integerPart, decimalPart] = fixedNum.split(".");

    // Apply Indian number formatting (lakhs and crores)
    const lastThreeDigits = integerPart.slice(-3);
    const otherDigits = integerPart.slice(0, -3);

    if (otherDigits !== "") {
        const formattedOtherDigits = otherDigits.replace(
            /\B(?=(\d{2})+(?!\d))/g,
            ","
        );
        return `${formattedOtherDigits},${lastThreeDigits}.${decimalPart}`;
    } else {
        return `${lastThreeDigits}.${decimalPart}`;
    }
};

const CashBillView: React.FC<CashBillViewProps> = ({ cashBillId }) => {
    const { OLK_PATH } = useEnvContext();
    const [cashBill, setCashBill] = useState<ApiResponse | null>(null);
    const [combinedData, setCombinedData] =
        useState<CashBillCombinedData | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const orgname = getOrgDataField("orgname");
    const orgCode = Number(getOrgDataField("orgcode"));

    useEffect(() => {
        const fetchCashBillAndOrgData = async () => {
            if (!OLK_PATH) {
                setError("API base URL is not configured");
                setLoading(false);
                return;
            }

            if (!cashBillId) {
                setError("No cash bill ID provided");
                setLoading(false);
                return;
            }

            setLoading(true);
            setError(null);
            try {
                // Fetch cash bill data
                const cashBillResponse = await apiCall<ApiResponse>("GET", `${OLK_PATH}/invoice/findinvoice?invid=${cashBillId}`);

                if (!cashBillResponse.data?.invrecord) {
                    throw new Error("No cash bill record found");
                }

                setCashBill(cashBillResponse.data);

                // Fetch organization data
                try {
                    const orgResponse = await apiCall<OrgBdtResponse>(
                                    "GET",
                                    `${OLK_PATH}/organisations/bdt?orgcode=${orgCode}`
                                );
                    // const orgResponse = await axiosInstance.get<details>(
                    //     "/organisations/bdt",
                    //     {
                    //         baseURL: OLK_PATH,
                    //         params: { orgcode: orgCode },
                    //     }
                    // );

                    // Combine cash bill data with organization data
                    const combined: CashBillCombinedData = {
                        ...cashBillResponse.data,
                        orgDetails: orgResponse.data,
                    };

                    setCombinedData(combined);
                } catch (orgErr) {
                    console.warn("Failed to fetch organization data:", orgErr);
                    // Still set combined data without org details
                    const combined: CashBillCombinedData = {
                        ...cashBillResponse.data,
                        orgDetails: null,
                    };
                    setCombinedData(combined);
                }
            } catch (err: unknown) {
                let errorMessage = "Failed to fetch cash bill details.";
                if (err instanceof AxiosError) {
                    if (err.response?.status === 404) {
                        errorMessage = `Cash bill not found for invid: ${cashBillId}`;
                    } else {
                        errorMessage = `Failed to fetch cash bill: ${err.message}`;
                    }
                } else if (err instanceof Error) {
                    errorMessage = `Failed to fetch cash bill: ${err.message}`;
                }

                setError(errorMessage);
                console.error("Error details:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchCashBillAndOrgData();
    }, [cashBillId, OLK_PATH, orgCode]);

    // console.log(combinedData);

    if (loading) {
        return (
            <ProgressSpinner
                style={{ display: "block", margin: "2rem auto" }}
            />
        );
    }

    if (error) {
        return (
            <Message
                severity="error"
                text={error}
                style={{ display: "block", margin: "2rem" }}
            />
        );
    }

    if (!cashBill || !cashBill.invrecord) {
        return (
            <Message
                severity="warn"
                text="No cash bill data available."
                style={{ display: "block", margin: "2rem" }}
            />
        );
    }

    return (
        <div className="cash-bill-view p-m-4">
            <
style>{`
        .cash-bill-view {
          padding: 0.5rem;
        }

        /* Mobile-specific styling for Products/Services table */
        @media (max-width: 768px) {
          .cash-bill-products-table {
            display: none; /* Hide DataTable on mobile */
          }

          .cash-bill-products-card-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.25rem;
            margin: 0 -0.25rem;
          }

          .cash-bill-product-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            min-height: 320px;
            width: 100%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }

          .cash-bill-product-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
          }

          .cash-bill-product-card div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.25rem 0;
          }

          .cash-bill-product-card label {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.95rem;
            flex-shrink: 0;
            min-width: 100px;
          }

          .cash-bill-product-card span {
            color: #4b5563;
            font-size: 0.95rem;
            text-align: right;
            word-break: break-word;
          }

          .cash-bill-product-card .product-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: #6366f1;
            text-align: right;
          }

          .cash-bill-product-card .total-amount {
            font-size: 1.05rem;
            font-weight: 700;
            color: #059669;
          }

          .cash-bill-product-card .divider-line {
            border-top: 1px solid #e5e7eb;
            margin: 0.5rem 0;
            padding: 0;
          }
        }

        /* Desktop-specific styling */
        @media (min-width: 769px) {
          .cash-bill-products-card-container {
            display: none;
          }

          .cash-bill-products-table {
            display: block;
          }
        }
      `}</style>
      
            <Card
                title={
                    cashBill.invrecord.inoutflag === 15
                        ? "Sales Cash Bill"
                        : cashBill.invrecord.inoutflag === 9
                        ? "Purchase Cash Bill"
                        : "Cash Bill"
                }
            >
                <div className="invoice-header">
                    <div className="invoice-header">{orgname}</div>
                    <div className="invoice-header">{`Cash Bill No : ${cashBill.invrecord.invoiceno}`}</div>
                    <div className="invoice-header">
                        {`Cash Bill Date : ${new Date(
                            cashBill.invrecord.invoicedate
                        )
                            .toLocaleDateString("en-GB", {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                            })
                            .split("/")
                            .join("-")}`}
                    </div>
                </div>
                <Divider />
                {/* Product Table */}
                <Panel header="Products/Services" className="p-mb-2 p-shadow-2">
                    <DataTable
                        value={cashBill.invrecord.contents}
                        className="p-datatable-sm cash-bill-products-table"
                        responsiveLayout="stack"
                        breakpoint="960px"
                        scrollable
                        scrollHeight="flex"
                        resizableColumns
                        columnResizeMode="fit"
                        showGridlines
                        stripedRows
                        size="small"
                    >
                        <Column
                            field="productname"
                            header="Product Name"
                            className="p-text-bold"
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="quantity"
                            header="Quantity"
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="freeQuantity"
                            header="Free Qty"
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="pricePerUnit"
                            header="Price/Unit"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.pricePerUnit.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="discountAmount"
                            header="Discount"
                            body={(rowData) => {
                                if (cashBill.invrecord.discflag === 1) {
                                    // Amount discount
                                    return `${(
                                        rowData.discountAmount || 0
                                    ).toFixed(2)}`;
                                } else if (cashBill.invrecord.discflag === 16) {
                                    // Percentage discount - calculate percentage from discountAmount
                                    const baseValue =
                                        rowData.quantity * rowData.pricePerUnit;
                                    if (
                                        baseValue > 0 &&
                                        rowData.discountAmount > 0
                                    ) {
                                        const discountPercentage =
                                            (rowData.discountAmount /
                                                baseValue) *
                                            100;
                                        return `${discountPercentage.toFixed(
                                            2
                                        )}%`;
                                    }
                                    return "0.00%";
                                } else {
                                    // Fallback for unknown discount types
                                    return `${(
                                        rowData.discountAmount || 0
                                    ).toFixed(2)}`;
                                }
                            }}
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="taxableAmount"
                            header="Taxable Amt"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.taxableAmount.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="gstrate"
                            header="GST Rate"
                            body={(rowData) => `${rowData.gstrate}%`}
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="gstamount"
                            header="GST Amount"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.gstamount.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="productAmount"
                            header="Total Amount"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.productAmount.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                    </DataTable>
                    {/* Card Layout for Mobile */}
                    <div className="cash-bill-products-card-container">
                        {cashBill.invrecord.contents.map((product, index) => (
                            <div key={index} className="cash-bill-product-card">
                                <div>
                                    <label>Product:</label>
                                    <span className="product-name">
                                        {product.productname}
                                    </span>
                                </div>
                                <div className="divider-line"></div>
                                <div>
                                    <label>Quantity:</label>
                                    <span>{product.quantity}</span>
                                </div>
                                <div>
                                    <label>Free Qty:</label>
                                    <span>{product.freeQuantity}</span>
                                </div>
                                <div>
                                    <label>Price/Unit:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            product.pricePerUnit.toFixed(2)
                                        )}
                                    </span>
                                </div>
                                <div>
                                    <label>Discount:</label>
                                    <span>
                                        {cashBill.invrecord.discflag === 1
                                            ? `₹${(
                                                  product.discountAmount || 0
                                              ).toFixed(2)}`
                                            : `${(
                                                  product.discountAmount || 0
                                              ).toFixed(2)}%`}
                                    </span>
                                </div>
                                <div>
                                    <label>Taxable Amt:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            product.taxableAmount.toFixed(2)
                                        )}
                                    </span>
                                </div>
                                <div>
                                    <label>GST Rate:</label>
                                    <span>{product.gstrate}%</span>
                                </div>
                                <div>
                                    <label>GST Amount:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            product.gstamount.toFixed(2)
                                        )}
                                    </span>
                                </div>
                                <div className="divider-line"></div>
                                <div>
                                    <label>Total Amount:</label>
                                    <span className="total-amount">
                                        {formatIndianCurrency(
                                            product.productAmount.toFixed(2)
                                        )}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </Panel>
                <Divider />

                {/* Payment Details and Cash Bill Summary Side by Side */}
                <div className="p-mb-2 p-grid">
                    {/* Payment Details */}
                    <div className="p-mb-2 p-col-12 p-md-6">
                        <Panel
                            header="Payment Details"
                            className="p-mb-2 p-shadow-2"
                        >
                            <div className="p-grid">
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Payment Mode:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {cashBill.invrecord.paymentmode ===
                                            3
                                                ? "Cash"
                                                : cashBill.invrecord
                                                      .paymentmode === 2
                                                ? "Bank/Online"
                                                : "Unknown"}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </Panel>
                    </div>

                    <Divider />
                    {/* Cash Bill Summary */}
                    <div className="p-col-12 p-md-6">
                        <Panel
                            header="Cash Bill Summary"
                            className="p-shadow-2"
                        >
                            <div className="p-field">
                                <label>
                                    <strong>Total Taxable Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        cashBill.invrecord.contents.reduce(
                                            (sum, item) =>
                                                sum + item.taxableAmount,
                                            0
                                        )
                                    )}
                                </span>
                            </div>
                            <div className="p-field">
                                <label>
                                    <strong>Total Discount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        cashBill.invrecord.contents.reduce(
                                            (sum, item) =>
                                                sum +
                                                (item.discountAmount || 0),
                                            0
                                        )
                                    )}
                                </span>
                            </div>

                            {cashBill.invrecord.taxstate ===
                            cashBill.invrecord.sourcestate ? (
                                <>
                                    <div className="p-field">
                                        <label>
                                            <strong>Total CGST:</strong>
                                        </label>
                                        <span className="ml-2">
                                            ₹
                                            {formatIndianCurrency(
                                                cashBill.invrecord.contents.reduce(
                                                    (sum, item) =>
                                                        sum + item.gstamount,
                                                    0
                                                ) / 2
                                            )}
                                        </span>
                                    </div>
                                    <div className="p-field">
                                        <label>
                                            <strong>Total SGST:</strong>
                                        </label>
                                        <span className="ml-2">
                                            ₹
                                            {formatIndianCurrency(
                                                cashBill.invrecord.contents.reduce(
                                                    (sum, item) =>
                                                        sum + item.gstamount,
                                                    0
                                                ) / 2
                                            )}
                                        </span>
                                    </div>
                                </>
                            ) : (
                                <div className="p-field">
                                    <label>
                                        <strong>Total IGST:</strong>
                                    </label>
                                    <span className="ml-2">
                                        ₹
                                        {formatIndianCurrency(
                                            cashBill.invrecord.contents.reduce(
                                                (sum, item) =>
                                                    sum + item.gstamount,
                                                0
                                            )
                                        )}
                                    </span>
                                </div>
                            )}

                            <div className="p-field">
                                <label>
                                    <strong>Total Cash Bill Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        cashBill.invrecord.invoicetotal
                                    )}
                                </span>
                            </div>
                            <div className="p-field">
                                <label>
                                    <strong>Total Paid Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        cashBill.invrecord.amountpaid
                                    )}
                                </span>
                            </div>
                        </Panel>
                    </div>
                </div>
                <Divider />
                {/* Cash Bill Summary */}
                <div className="p-field">
                    <label className="p-d-block">
                        <strong>Total in Words : </strong>
                    </label>
                    <span>{cashBill.invrecord.invoicetotalword || "N/A"}</span>
                </div>
                <div className="p-field">
                    <label className="p-d-block">
                        <strong>Narration : </strong>
                    </label>
                    <span>{cashBill.invrecord.invnarration || ""}</span>
                </div>
            </Card>
            {/* Print Button */}
            {cashBill && combinedData && (
                <div className="flex">
                    {/* PDF download button - only show when we have combined data */}
                    <PDFDownloadLink
                        document={<CashBillPDF data={combinedData} />}
                        fileName={`cashbill_${cashBill.invrecord.invoiceno}.pdf`}
                    >
                        {({ loading }) => (
                            <Button
                                text
                                raised
                                label={loading ? "Generating PDF..." : "Print"}
                                icon={
                                    loading
                                        ? "pi pi-spinner pi-spin"
                                        : "pi pi-print"
                                }
                                className="w-full sm:w-auto bg-blue-600 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
                                disabled={loading}
                            />
                        )}
                    </PDFDownloadLink>
                </div>
            )}

            {cashBill && !combinedData && (
                <div className="flex">
                    <Button
                        text
                        raised
                        label="Loading PDF data..."
                        icon="pi pi-spinner pi-spin"
                        className="w-full sm:w-auto bg-gray-400 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
                        disabled={true}
                    />
                </div>
            )}
        </div>
    );
};

export default CashBillView;
