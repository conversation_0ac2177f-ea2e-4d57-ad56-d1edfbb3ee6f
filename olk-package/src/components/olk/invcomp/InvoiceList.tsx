"use client";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Button } from "primereact/button";
import { RadioButton } from "primereact/radiobutton";
import { Message } from "primereact/message";
import { getOrgDataField } from "../../../utils/cookies";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import apiCall from "../../../utils/apiCallService";

interface Invoice {
    id: string;
    invoiceNo: string;
    invoiceDate: string;
    partyName: string;
    taxableAmount: string;
    gst: string;
    totalAmount: string;
    amountPaid: string;
}

interface ApiInvoice {
    invid: number;
    invoiceno: string;
    invoicedate: string;
    partyname: string;
    invoicetotal: string;
    nettotal: string;
    gst: string;
    amountpaid: string;
}

interface ApiResponse {
    olkstatus: number;
    invoices: ApiInvoice[];
}

type PaymentFilter = "all" | "paid" | "unpaid";

const InvoiceList: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const title = searchParams.get("title");
    const [invoices, setInvoices] = useState<Invoice[]>([]);
    const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>([]);
    const [invoiceType, setInvoiceType] = useState<"sales" | "purchase">(
        "sales"
    );
    const [paymentFilter, setPaymentFilter] = useState<PaymentFilter>("all");
    const [error, setError] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const { OLK_PATH } = useEnvContext();
    const orgcode = Number(getOrgDataField("orgcode"));

    // Get year start and end dates from cookies and format them correctly
    const isoString1 = getOrgDataField("yearstart");
    const yearstart = isoString1
        ? new Date(isoString1).toISOString().split("T")[0] // Format: YYYY-MM-DD
        : "";
    const isoString = getOrgDataField("yearend");
    const yearend = isoString
        ? new Date(isoString).toISOString().split("T")[0] // Format: YYYY-MM-DD
        : "";

    if (!orgcode) return;

    useEffect(() => {
        if (title === "Details of Sales Invoice") {
            setInvoiceType("sales");
        } else {
            setInvoiceType("purchase");
        }
    }, [title]);

    useEffect(() => {
        const fetchInvoices = async () => {
            setInvoices([]); // Clear previous invoices
            setLoading(true);
            setError(null);
            try {
                const inoutflag = invoiceType === "sales" ? 15 : 9;
                const response = await apiCall<ApiResponse>(
                    "GET",
                    `${OLK_PATH}/invoice?orgcode=${orgcode}&inoutflag=${inoutflag}&yearstart=${yearstart}&enddate=${yearend}`
                );

                // Log response details
                // console.log(`${invoiceType.toUpperCase()} Invoices Response:`, {
                //     status: response.status,
                //     data: response.data,
                //     invoiceCount: response.data?.invoices?.length || 0,
                //     firstInvoice: response.data?.invoices?.[0],
                //     lastInvoice:
                //         response.data?.invoices?.[
                //             response.data.invoices.length - 1
                //         ],
                // });

                if (
                    !response.data?.invoices ||
                    !Array.isArray(response.data.invoices)
                ) {
                    throw new Error(
                        `Invalid response format: ${JSON.stringify(
                            response.data
                        )}`
                    );
                }

                const mappedInvoices: Invoice[] = response.data.invoices.map(
                    (inv: ApiInvoice) => {
                        // Parse DD-MM-YYYY format safely
                        let formattedDate = inv.invoicedate;
                        if (inv.invoicedate && inv.invoicedate.includes("-")) {
                            const parts = inv.invoicedate.split("-");
                            if (parts.length === 3) {
                                const [day, month, year] = parts;
                                formattedDate = `${day}/${month}/${year}`;
                            }
                        }

                        return {
                            id: inv.invid.toString(),
                            invoiceNo: inv.invoiceno,
                            invoiceDate: formattedDate,
                            partyName: inv.partyname,
                            taxableAmount: inv.nettotal,
                            gst: inv.gst,
                            totalAmount: inv.invoicetotal,
                            amountPaid: inv.amountpaid,
                        };
                    }
                );
                // Log mapped invoices summary
                // console.log(`${invoiceType.toUpperCase()} Invoices Summary:`, {
                //     totalInvoices: mappedInvoices.length,
                //     paidInvoices: mappedInvoices.filter(
                //         (inv) =>
                //             parseFloat(inv.totalAmount) ===
                //             parseFloat(inv.amountPaid)
                //     ).length,
                //     unpaidInvoices: mappedInvoices.filter(
                //         (inv) =>
                //             parseFloat(inv.totalAmount) >
                //             parseFloat(inv.amountPaid)
                //     ).length,
                // });

                setInvoices(mappedInvoices);
                setFilteredInvoices(mappedInvoices);
            } catch (err) {
                const errorMessage =
                    err instanceof Error
                        ? err.message
                        : "Failed to fetch invoices. Please try again.";
                setError(errorMessage);
                console.error(`Error fetching ${invoiceType} invoices:`, err);
            } finally {
                setLoading(false);
            }
        };

        // Only fetch if we have valid dates
        if (yearstart && yearend) {
            fetchInvoices();
        } else {
            setError("Invalid date range configuration");
            setLoading(false);
        }
    }, [invoiceType, yearstart, yearend, orgcode, OLK_PATH]);

    // Apply payment filter when invoices or payment filter changes
    useEffect(() => {
        if (invoices.length === 0) {
            setFilteredInvoices([]);
            return;
        }

        let result = [...invoices];

        if (paymentFilter === "paid") {
            result = invoices.filter(
                (inv) =>
                    parseFloat(inv.totalAmount) === parseFloat(inv.amountPaid)
            );
        } else if (paymentFilter === "unpaid") {
            result = invoices.filter(
                (inv) =>
                    parseFloat(inv.totalAmount) > parseFloat(inv.amountPaid)
            );
        }

        setFilteredInvoices(result);
    }, [invoices, paymentFilter]);

    const actionBodyTemplate = (rowData: Invoice) => {
        return (
            <div className="flex justify-center items-center w-full">
                <Button
                    icon="pi pi-eye"
                    className="p-button-sm p-button-text !flex !items-center !justify-center !p-2 m-auto"
                    onClick={() => {
                        router.push(
                            `/dashboard/invoice/sales-and-purchase/view/${rowData.id}`
                        );
                    }}
                />
            </div>
        );
    };

    const amountPaidBodyTemplate = (rowData: Invoice) => {
        return (
            <span
                className={
                    parseFloat(rowData.totalAmount) >
                    parseFloat(rowData.amountPaid)
                        ? "text-red-500"
                        : "text-gray-800"
                }
            >
                {rowData.amountPaid}
            </span>
        );
    };

    return (
        <div className="invoice-list-container">
            <style>{`
        .invoice-list-container {
          padding: 0.5rem;
        }

        .radio-inline {
          display: flex;
          gap: 0.75rem;
          margin-bottom: 1rem;
        }

        .filter-buttons {
          display: flex;
          justify-content: flex-end;
          gap: 0.5rem;
          margin-bottom: 1rem;
        }

        .filter-button {
          padding: 0.5rem 1rem;
          border-radius: 0.375rem;
          font-weight: 500;
          transition: all 0.2s;
          border: 1px solid #e2e8f0;
        }

        .filter-button.active {
          background-color: #4f46e5;
          color: white;
          border-color: #4f46e5;
        }

        .filter-button:not(.active) {
          background-color: white;
          color: #4b5563;
        }

        .filter-button:hover:not(.active) {
          background-color: #f3f4f6;
        }

       /* Mobile-specific styling */
  @media (max-width: 768px) {
  .invoice-table {
    display: none; /* Hide DataTable on mobile */
  }

   .invoice-card-container {
            display: flex;
            flex-direction: column;
            gap: 0.50rem; /* Increased gap for better separation */
            padding: 0.50rem; /* Slightly more padding */
          }

          .invoice-card {
            background: #ffffff;
            border: 1px solid #e2e8f0; /* Softer border color */
            border-radius: 16px; /* Larger rounded corners */
            padding: 0.75rem; /* Increased padding for larger card */
            min-height: 220px; /* Minimum height for larger card */
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08); /* Stronger shadow for depth */
            transition: transform 0.2s ease, box-shadow 0.2s ease; /* Smooth hover/tap effect */
          }

  .invoice-card:hover {
    transform: translateY(-2px); /* Slight lift on hover/tap */
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); /* Enhanced shadow on hover */
  }

  .invoice-card div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem; /* Increased spacing between rows */
          }

          .invoice-card label {
            font-weight: 600; /* Bold for emphasis */
            color: #1f2937; /* Darker gray for contrast */
            font-size: 1rem; /* Larger font for readability */
          }

          .invoice-card span {
            color: #4b5563; /* Softer gray for secondary text */
            font-size: 1rem; /* Larger font for readability */
          }

          .invoice-card .invoice-no {
            font-size: 1.25rem; /* Larger for invoice number */
            font-weight: 700; /* Bold for emphasis */
            color: #6366f1; /* Brand color */
          }

          .invoice-card .party-name {
            font-size: 1rem; /* Slightly larger for party name */
            font-weight: 600;
            color: #111827; /* Darker for prominence */
          }

          .invoice-card .action {
            justify-content: flex-end;
            gap: 0.75rem; /* Increased spacing between buttons */
          }

         .invoice-card .action .p-button .p-button-text .pi-eye {
          width: 2.75rem;
           height: 2.75rem;
            display: flex;
            align-items: center;
           justify-content: center;
          font-size: 1rem;
          padding: 0; /* Remove default padding */
          line-height: 1;
          background: transparent;
}
        }

        /* Desktop-specific styling */
        @media (min-width: 769px) {
          .invoice-card-container {
            display: none; /* Hide cards on desktop */
          }

          .invoice-table {
            display: block; /* Ensure DataTable is visible */
          }
        }
      `}</style>

            <div className="radio-inline mt-4">
                <h2 className="text-xl md:text-2xl font-semibold">Invoices</h2>
                <div key="sales">
                    <RadioButton
                        inputId="sales"
                        name="invoiceType"
                        value="sales"
                        onChange={(e) => {
                            setInvoiceType(e.value);
                        }}
                        checked={invoiceType === "sales"}
                    />
                    <label htmlFor="sales" className="ml-2">
                        Sales
                    </label>
                </div>
                <div key="purchase">
                    <RadioButton
                        inputId="purchase"
                        name="invoiceType"
                        value="purchase"
                        onChange={(e) => {
                            setInvoiceType(e.value);
                        }}
                        checked={invoiceType === "purchase"}
                    />
                    <label htmlFor="purchase" className="ml-2">
                        Purchase
                    </label>
                </div>
            </div>

            <div className="filter-buttons">
                <button
                    className={`filter-button cursor-pointer ${
                        paymentFilter === "all" ? "active" : ""
                    }`}
                    onClick={() => setPaymentFilter("all")}
                >
                    All
                </button>
                <button
                    className={`filter-button cursor-pointer ${
                        paymentFilter === "paid" ? "active" : ""
                    }`}
                    onClick={() => setPaymentFilter("paid")}
                >
                    Paid
                </button>
                <button
                    className={`filter-button cursor-pointer ${
                        paymentFilter === "unpaid" ? "active" : ""
                    }`}
                    onClick={() => setPaymentFilter("unpaid")}
                >
                    Unpaid
                </button>
            </div>

            {error && (
                <Message severity="error" text={error} className="p-mb-4" />
            )}

            {/* DataTable for Desktop */}
            <div className="invoice-table">
                <DataTable
                    value={filteredInvoices}
                    paginator
                    rows={15}
                    loading={loading}
                    emptyMessage="No invoices found."
                    className="p-datatable-sm"
                    scrollable
                    scrollHeight="calc(100vh - 100px)"
                    resizableColumns
                    columnResizeMode="fit"
                    showGridlines
                >
                    <Column
                        field="invoiceNo"
                        header="Invoice No."
                        sortable
                        style={{ minWidth: "120px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span className="font-medium">
                                {rowData.invoiceNo}
                            </span>
                        )}
                    />
                    <Column
                        field="invoiceDate"
                        header="Invoice Date"
                        sortable
                        style={{ minWidth: "120px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span className="text-gray-800">
                                {rowData.invoiceDate}
                            </span>
                        )}
                    />
                    <Column
                        field="partyName"
                        header="Party Name"
                        sortable
                        style={{ maxWidth: "180px", whiteSpace: "normal" }}
                        bodyClassName="wrap-column"
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span className="text-gray-800">
                                {rowData.partyName}
                            </span>
                        )}
                    />
                    <Column
                        field="taxableAmount"
                        header="Taxable Amount"
                        sortable
                        style={{ minWidth: "140px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span className="text-gray-800 font-medium">
                                {parseFloat(
                                    rowData.taxableAmount
                                ).toLocaleString("en-IN", {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2,
                                })}
                            </span>
                        )}
                    />
                    <Column
                        field="gst"
                        header="GST"
                        sortable
                        style={{ minWidth: "100px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span className="text-gray-800 font-medium">
                                {parseFloat(rowData.gst).toLocaleString(
                                    "en-IN",
                                    {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    }
                                )}
                            </span>
                        )}
                    />
                    <Column
                        field="totalAmount"
                        header="Total Amount"
                        sortable
                        style={{ minWidth: "140px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span className="text-gray-800 font-medium">
                                {parseFloat(rowData.totalAmount).toLocaleString(
                                    "en-IN",
                                    {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    }
                                )}
                            </span>
                        )}
                    />
                    <Column
                        field="amountPaid"
                        header="Amount Paid"
                        sortable
                        style={{ minWidth: "140px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                        body={(rowData) => (
                            <span
                                className={
                                    parseFloat(rowData.totalAmount) >
                                    parseFloat(rowData.amountPaid)
                                        ? "text-red-500 font-medium"
                                        : "text-gray-800 font-medium"
                                }
                            >
                                {parseFloat(rowData.amountPaid).toLocaleString(
                                    "en-IN",
                                    {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    }
                                )}
                            </span>
                        )}
                    />
                    <Column
                        body={actionBodyTemplate}
                        header="Action"
                        style={{ minWidth: "80px" }}
                        headerStyle={{
                            textAlign: "center",
                            backgroundColor: "#bfdbfe",
                        }}
                    />
                </DataTable>
            </div>

            {/* Card Layout for Mobile */}
            <div className="invoice-card-container">
                {loading ? (
                    <div className="text-center text-gray-500 text-lg">
                        Loading...
                    </div>
                ) : filteredInvoices.length === 0 ? (
                    <div className="text-center text-gray-500 text-lg">
                        No invoices found.
                    </div>
                ) : (
                    filteredInvoices.map((invoice, idx) => (
                        <div key={invoice.id} className="invoice-card">
                            <div>
                                <span className="invoice-no">
                                    {invoice.invoiceNo}
                                </span>
                                <span>{invoice.invoiceDate}</span>
                            </div>
                            <div>
                                <span className="party-name">
                                    {invoice.partyName}
                                </span>

                                <Button
                                    icon="pi pi-eye"
                                    className="p-button-sm p-button-text action"
                                    onClick={() =>
                                        router.push(
                                            `/dashboard/invoice/sales-and-purchase/view/${invoice.id}`
                                        )
                                    }
                                    aria-label="View Invoice"
                                />
                            </div>
                            <div>
                                <label>Taxable Amount: </label>
                                <span className="text-gray-800">
                                    {parseFloat(
                                        invoice.taxableAmount
                                    ).toLocaleString("en-IN", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    })}
                                </span>
                            </div>
                            <div>
                                <label>GST: </label>
                                <span className="text-gray-800">
                                    {parseFloat(invoice.gst).toLocaleString(
                                        "en-IN",
                                        {
                                            minimumFractionDigits: 2,
                                            maximumFractionDigits: 2,
                                        }
                                    )}
                                </span>
                            </div>
                            <div>
                                <label>Total Amount: </label>
                                <span className="text-gray-800">
                                    {parseFloat(
                                        invoice.totalAmount
                                    ).toLocaleString("en-IN", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    })}
                                </span>
                            </div>
                            <div>
                                <label>Amount Paid:</label>
                                <span
                                    className={
                                        parseFloat(invoice.totalAmount) >
                                        parseFloat(invoice.amountPaid)
                                            ? "text-red-500"
                                            : "text-gray-800"
                                    }
                                >
                                    {parseFloat(
                                        invoice.amountPaid
                                    ).toLocaleString("en-IN", {
                                        minimumFractionDigits: 2,
                                        maximumFractionDigits: 2,
                                    })}
                                </span>
                            </div>
                            <div>
                                <label>Status:</label>
                                <span
                                    className={
                                        parseFloat(invoice.totalAmount) >
                                        parseFloat(invoice.amountPaid)
                                            ? "bg-red-100 text-red-600 px-2 py-1  text-sm"
                                            : "bg-green-100 text-green-600 px-2 py-1 text-sm"
                                    }
                                    style={{ borderRadius: "4px" }}
                                >
                                    {parseFloat(invoice.totalAmount) >
                                    parseFloat(invoice.amountPaid)
                                        ? "Unpaid"
                                        : "Paid"}
                                </span>
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default InvoiceList;
