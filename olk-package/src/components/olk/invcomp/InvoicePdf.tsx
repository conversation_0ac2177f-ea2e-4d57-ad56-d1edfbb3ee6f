"use client";

import {
    Page,
    Text,
    View,
    Document,
    StyleSheet,
    Image,
} from "@react-pdf/renderer";
import { getOrgDataField } from "../../../utils/cookies";
import { ApiResponse } from "./InvoiceView";

// Define invoice preferences interface
interface InvoicePrefs {
    logo?: string;
    sign?: string;
    autoinvno?: number;
    tandc?: string;
}

// Define orgDetails interfaces (same as InvoiceView)
interface orgFields {
    bankdetails: null | string;
    gstin: string;
    orgpan: string;
    orgaddr: string;
    orgpincode: string;
    invoice_preferences?: InvoicePrefs;
}

interface details {
    bisdetails: orgFields;
    olkstatus?: number; // Matches JSON structure
}

interface CombinedData extends ApiResponse {
    orgDetails: details | null;
}

// Create styles
const styles = StyleSheet.create({
    page: {
        fontSize: 10,
        padding: 30,
    },
    titleContainer: {
        textAlign: "center",
        marginBottom: 16,
    },
    title: {
        fontSize: 14,
        fontWeight: "bold",
        marginBottom: 8,
    },
    container: {
        marginVertical: 12,
        paddingVertical: 8,
    },
    header: {
        marginBottom: 5,
        borderBottomWidth: 1,
        borderBottomColor: "#000",
        paddingBottom: 5,
    },
    flexRow: {
        flexDirection: "row",
        justifyContent: "space-between",
    },
    flexCol: {
        flexDirection: "column",
    },
    billToContainer: {
        flexDirection: "row",
        gap: 24,
        justifyContent: "space-between",
        marginVertical: 12,
    },
    sectionContainer: {
        flex: 1,
        flexDirection: "column",
    },
    verticalDivider: {
        height: "auto",
        width: 1,
        backgroundColor: "#c2c2c2",
    },
    sectionTitle: {
        marginBottom: 4,
        fontWeight: "semibold",
        fontSize: 11,
    },
    boldText: {
        fontWeight: 500,
    },
    table: {
        width: "100%",
        marginVertical: 12,
        borderWidth: 1,
        borderColor: "#000",
    },
    tableHeader: {
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "#a9b0ba",
        fontWeight: "semibold",
        backgroundColor: "#f5f5f5",
    },
    tableSubHeader: {
        flexDirection: "row",
        borderBottomWidth: 1,
        borderBottomColor: "black",
        fontWeight: "semibold",
        backgroundColor: "#f5f5f5",
    },
    tableRow: {
        flexDirection: "row",
        borderBottomWidth: 0.5,
        borderBottomColor: "#94979c",
    },
    tableTotalRow: {
        flexDirection: "row",
        borderTopWidth: 1,
        borderTopColor: "#000",
        fontWeight: "semibold",
    },
    cell: {
        padding: 3,
        textAlign: "center",
        borderRightWidth: 1,
        borderColor: "#000",
    },
    serialCell: {
        width: "5%",
    },
    descriptionCell: {
        width: "18%",
    },
    qtyCell: {
        width: "8%",
    },
    amountCell: {
        width: "12%",
    },
    gstCell: {
        width: "9.5%",
    },
    footer: {
        marginTop: 4,
        borderTopWidth: 1,
        borderTopColor: "#d3d3d3",
        paddingTop: 10,
        textAlign: "center",
    },
    footerText: {
        fontSize: 9,
        color: "#555555",
        marginBottom: 4,
    },
    paymentContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginVertical: 12,
        gap: 24,
        padding: 10,
        borderRadius: 4,
    },
    transportContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginVertical: 12,
        gap: 24,
    },
    logo: {
        width: 80,
        height: 60,
        objectFit: "contain",
        marginBottom: 8,
    },
    signature: {
        width: 100,
        height: 50,
        objectFit: "contain",
        marginTop: 8,
    },
});

const InvoicePdf = ({ data }: { data: CombinedData }) => {
    console.log(data);

    // Safety check - ensure we have valid invoice data
    if (!data || !data.invrecord) {
        return (
            <Document>
                <Page size="A4" style={styles.page}>
                    <View style={styles.titleContainer}>
                        <Text style={styles.title}>
                            Invoice data not available
                        </Text>
                    </View>
                </Page>
            </Document>
        );
    }

    const { invrecord, custdetails, orgDetails } = data;
    const orgname = getOrgDataField("orgname");
    const orgstate = getOrgDataField("orgstate");

    // Supported image formats for invoice logos and signatures
    const SUPPORTED_IMAGE_FORMATS = ["png", "jpeg", "jpg", "svg"];
    const MAX_IMAGE_SIZE_KB = 1024; // 1MB max

    // Standard dimensions for different image types
    const STANDARD_DIMENSIONS = {
        logo: { width: 80, height: 60 },
        signature: { width: 100, height: 50 },
    };

    // Helper function to detect image format from base64 data
    const detectImageFormat = (base64String: string): string => {
        // Check the first few characters of base64 to detect format
        const header = base64String.substring(0, 20);

        // Common image format signatures in base64
        if (header.startsWith("/9j/") || header.startsWith("iVBORw0KGgo")) {
            return header.startsWith("/9j/") ? "jpeg" : "png";
        }

        // Try to decode and check magic bytes and content
        try {
            const binaryString = atob(base64String.substring(0, 100)); // Check more bytes for SVG
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // PNG signature: 89 50 4E 47
            if (
                bytes[0] === 0x89 &&
                bytes[1] === 0x50 &&
                bytes[2] === 0x4e &&
                bytes[3] === 0x47
            ) {
                return "png";
            }
            // JPEG signature: FF D8 FF
            if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
                return "jpeg";
            }

            // SVG detection - check for SVG content in decoded string
            const decodedContent = binaryString.toLowerCase();
            if (
                decodedContent.includes("<svg") ||
                decodedContent.includes("<?xml")
            ) {
                return "svg+xml";
            }
        } catch (e) {
            console.warn("Could not detect image format, defaulting to png");
        }

        return "png"; // Default fallback
    };

    // Helper function to validate image format
    const isValidImageFormat = (format: string): boolean => {
        const normalizedFormat = format.toLowerCase();
        // Handle svg+xml format
        if (normalizedFormat === "svg+xml") {
            return SUPPORTED_IMAGE_FORMATS.includes("svg");
        }
        return SUPPORTED_IMAGE_FORMATS.includes(normalizedFormat);
    };

    // Helper function to validate and render base64 image with proper format detection
    const renderBase64Image = (
        base64String: string | undefined,
        style: any,
        altText: string = "Image not available"
    ) => {
        if (!base64String || typeof base64String !== "string") {
            return altText ? <Text>{altText}</Text> : null;
        }

        try {
            // Basic validation - check if it's a reasonable length
            const cleanString = base64String.trim();
            if (cleanString.length === 0) {
                return altText ? <Text>{altText}</Text> : null;
            }

            // Check file size (base64 is ~33% larger than original)
            const estimatedSizeKB = (cleanString.length * 0.75) / 1024;
            if (estimatedSizeKB > MAX_IMAGE_SIZE_KB) {
                console.warn(
                    `Image too large: ${estimatedSizeKB.toFixed(
                        2
                    )}KB, max allowed: ${MAX_IMAGE_SIZE_KB}KB`
                );
                return <Text>Image too large (max {MAX_IMAGE_SIZE_KB}KB)</Text>;
            }

            // Detect image format
            const imageFormat = detectImageFormat(cleanString);
            console.log(
                `Detected image format: ${imageFormat}, size: ${estimatedSizeKB.toFixed(
                    2
                )}KB`
            );

            // Validate image format
            if (!isValidImageFormat(imageFormat)) {
                console.warn(`Unsupported image format: ${imageFormat}`);
                return <Text>Unsupported image format: {imageFormat}</Text>;
            }

            // Create data URL with proper MIME type
            const mimeType =
                imageFormat === "svg+xml"
                    ? "image/svg+xml"
                    : `image/${imageFormat}`;
            const dataUrl = `data:${mimeType};base64,${cleanString}`;

            // Determine if this is a logo or signature based on style
            const isSignature =
                style?.width === STANDARD_DIMENSIONS.signature.width;
            const defaultDimensions = isSignature
                ? STANDARD_DIMENSIONS.signature
                : STANDARD_DIMENSIONS.logo;

            // Try to render the image with error handling and standard dimensions
            return (
                <Image
                    src={dataUrl}
                    style={{
                        // Use provided style dimensions, with fallback to standard dimensions
                        width: style?.width || defaultDimensions.width,
                        height: style?.height || defaultDimensions.height,
                        objectFit: "contain",
                        ...style, // Apply other style properties
                    }}
                />
            );
        } catch (error) {
            console.error("Error rendering image:", error);
            return altText ? <Text>{altText}</Text> : null;
        }
    };

    // Log orgDetails to verify
    console.log("Org details in render:", orgDetails);
    console.log(
        "Invoice preferences:",
        orgDetails?.bisdetails?.invoice_preferences
    );

    // Format date
    const formatDate = (dateString: string | null) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        return date.toLocaleDateString("en-IN");
    };

    // Determine invoice type
    const invoiceType = () => {
        switch (invrecord.inoutflag) {
            case 15:
                return "SALES INVOICE";
            case 9:
                return "PURCHASE INVOICE";
            default:
                return "INVOICE";
        }
    };

    // Determine payment mode
    const paymentMode = () => {
        switch (invrecord.paymentmode) {
            case 15:
                return "On Credit";
            case 3:
                return "Cash Received";
            case 2:
                return "Bank Transfer";
            default:
                return "Unknown";
        }
    };

    return (
        <Document>
            <Page size="A4" style={styles.page}>
                {/* Title with proper spacing */}
                <View style={styles.titleContainer}>
                    <Text style={styles.title}>{invoiceType()}</Text>
                </View>

                {/* Header */}
                <View style={[styles.container, styles.header]}>
                    <View style={styles.flexRow}>
                        {/* Left side: Logo and Company details in horizontal layout */}
                        <View
                            style={{
                                flexDirection: "row",
                                alignItems: "flex-start",
                                flex: 1,
                            }}
                        >
                            {/* Logo on the left */}
                            {invrecord?.inoutflag === 15 &&
                                orgDetails?.bisdetails?.invoice_preferences?.logo && (
                                    <View style={{ marginRight: 5, marginTop: 5 }}>
                                        {renderBase64Image(
                                            orgDetails.bisdetails.invoice_preferences.logo,
                                            styles.logo,
                                            "" // Empty string for no logo text
                                        )}
                                    </View>
                            )}

                            {/* Company details on the right of logo */}
                            <View style={{ flex: 1 }}>
                                <Text style={styles.sectionTitle}>
                                    {orgname || "N/A"}
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>
                                        Address:{" "}
                                    </Text>
                                    {orgDetails?.bisdetails?.orgaddr || "N/A"}
                                </Text>
                                <Text>
                                    {orgstate || "N/A"},{" "}
                                    {orgDetails?.bisdetails?.orgpincode ||
                                        "N/A"}
                                </Text>
                                {orgDetails?.bisdetails?.gstin && (
                                    <Text>
                                        <Text style={styles.boldText}>
                                            GSTIN:{" "}
                                        </Text>
                                        {orgDetails.bisdetails.gstin}
                                    </Text>
                                )}
                                {orgDetails?.bisdetails?.orgpan && (
                                    <Text>
                                        <Text style={styles.boldText}>
                                            PAN:{" "}
                                        </Text>
                                        {orgDetails.bisdetails.orgpan}
                                    </Text>
                                )}
                            </View>
                        </View>
                        <View
                            style={[
                                styles.flexCol,
                                { justifyContent: "flex-end" },
                            ]}
                        >
                            <Text>Invoice No: {invrecord.invoiceno}</Text>
                            <Text>
                                <Text style={styles.boldText}>
                                    Invoice Date:{" "}
                                </Text>
                                {formatDate(invrecord.invoicedate)}
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Bill To and Consignee */}
                <View style={[styles.container, styles.billToContainer]}>
                    <View style={styles.sectionContainer}>
                        <Text style={styles.sectionTitle}>
                            {invoiceType() === "SALES INVOICE"
                                ? "Bill to"
                                : "Supplier Details"}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Name: </Text>
                            {custdetails?.custname || "N/A"}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Address: </Text>
                            {custdetails?.custaddr || "N/A"},{" "}
                            {custdetails?.state || "N/A"},{" "}
                            {custdetails?.pincode || "N/A"}
                        </Text>
                        {custdetails?.gstin && (                            
                                <Text>
                                <Text style={styles.boldText}>GSTIN: </Text>
    {Object.values(custdetails.gstin)[0] || "N/A"}
                            </Text>
                        )}
                        <Text>
                            <Text style={styles.boldText}>Pan: </Text>N/A
                        </Text>
                    </View>

                    {invrecord.consignee && (
                        <>
                            <View style={styles.verticalDivider} />
                            <View style={styles.sectionContainer}>
                                <Text style={styles.sectionTitle}>
                                    Consignee Details
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>Name: </Text>
                                    {invrecord.consignee.name || "N/A"}
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>
                                        Address:{" "}
                                    </Text>
                                    {invrecord.consignee.address || "N/A"},{" "}
                                    {invrecord.consignee.state || "N/A"},{" "}
                                    {invrecord.consignee.pincode || "N/A"}
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>GST: </Text>
                                    N/A
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>Pan: </Text>
                                    N/A
                                </Text>
                            </View>
                        </>
                    )}
                </View>

                {/* Invoice Items Table */}
                <View style={styles.table}>
                    <View style={styles.tableHeader}>
                        <View style={[styles.cell, styles.serialCell]}>
                            <Text>S.no</Text>
                        </View>
                        <View style={[styles.cell, styles.descriptionCell]}>
                            <Text>DESCRIPTION</Text>
                        </View>
                        <View style={[styles.cell, styles.qtyCell]}>
                            <Text>QTY</Text>
                        </View>
                        <View style={[styles.cell, styles.qtyCell]}>
                            <Text>FREE QTY</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>AMOUNT</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>DISCOUNT AMOUNT</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>TAXABLE AMOUNT</Text>
                        </View>
                        <View style={[styles.cell, { width: "19%" }]}>
                            <Text>GST</Text>
                        </View>
                        <View
                            style={[
                                styles.cell,
                                styles.amountCell,
                                { borderRight: 0 },
                            ]}
                        >
                            <Text>TOTAL</Text>
                        </View>
                    </View>

                    <View style={styles.tableSubHeader}>
                        <View style={[styles.cell, styles.serialCell]}></View>
                        <View style={[styles.cell, { width: "18%" }]}></View>
                        <View style={[styles.cell, { width: "8%" }]}></View>
                        <View style={[styles.cell, { width: "8%" }]}></View>
                        <View style={[styles.cell, { width: "12%" }]}></View>
                        <View style={[styles.cell, { width: "12%" }]}></View>
                        <View style={[styles.cell, { width: "12%" }]}></View>
                        <View style={[styles.cell, styles.gstCell]}>
                            <Text>Rate</Text>
                        </View>
                        <View style={[styles.cell, styles.gstCell]}>
                            <Text>Amt</Text>
                        </View>
                        <View
                            style={[
                                styles.cell,
                                { width: "12%", borderRight: 0 },
                            ]}
                        ></View>
                    </View>

                    {invrecord.contents.map((item, index) => (
                        <View key={index} style={styles.tableRow}>
                            <View style={[styles.cell, styles.serialCell]}>
                                <Text>{index + 1}</Text>
                            </View>
                            <View style={[styles.cell, styles.descriptionCell]}>
                                <Text>{item.productname}</Text>
                            </View>
                            <View style={[styles.cell, styles.qtyCell]}>
                                <Text>{item.quantity}</Text>
                            </View>
                            <View style={[styles.cell, styles.qtyCell]}>
                                <Text>{item.freeQuantity}</Text>
                            </View>
                            <View style={[styles.cell, styles.amountCell]}>
                                <Text>{item.productAmount.toFixed(2)}</Text>
                            </View>
                            <View style={[styles.cell, styles.amountCell]}>
                                <Text>{item.discountAmount.toFixed(2)}</Text>
                            </View>
                            <View style={[styles.cell, styles.amountCell]}>
                                <Text>{item.taxableAmount.toFixed(2)}</Text>
                            </View>
                            <View style={[styles.cell, styles.gstCell]}>
                                <Text>{item.gstrate}%</Text>
                            </View>
                            <View style={[styles.cell, styles.gstCell]}>
                                <Text>{item.gstamount.toFixed(2)}</Text>
                            </View>
                            <View
                                style={[
                                    styles.cell,
                                    styles.amountCell,
                                    { borderRight: 0 },
                                ]}
                            >
                                <Text>
                                    {(
                                        item.taxableAmount + item.gstamount
                                    ).toFixed(2)}
                                </Text>
                            </View>
                        </View>
                    ))}

                    <View style={styles.tableTotalRow}>
                        <View
                            style={[
                                styles.cell,
                                styles.serialCell,
                                { borderRight: 0 },
                            ]}
                        ></View>
                        <View style={[styles.cell, { width: "34%" }]}>
                            <Text>TOTAL</Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>
                                {invrecord.contents
                                    .reduce(
                                        (sum, item) => sum + item.productAmount,
                                        0
                                    )
                                    .toFixed(2)}
                            </Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>
                                {invrecord.contents
                                    .reduce(
                                        (sum, item) =>
                                            sum + item.discountAmount,
                                        0
                                    )
                                    .toFixed(2)}
                            </Text>
                        </View>
                        <View style={[styles.cell, styles.amountCell]}>
                            <Text>
                                {invrecord.contents
                                    .reduce(
                                        (sum, item) => sum + item.taxableAmount,
                                        0
                                    )
                                    .toFixed(2)}
                            </Text>
                        </View>
                        <View style={[styles.cell, styles.gstCell]}></View>
                        <View style={[styles.cell, styles.gstCell]}>
                            <Text>
                                {invrecord.contents
                                    .reduce(
                                        (sum, item) => sum + item.gstamount,
                                        0
                                    )
                                    .toFixed(2)}
                            </Text>
                        </View>
                        <View
                            style={[
                                styles.cell,
                                styles.amountCell,
                                { borderRight: 0 },
                            ]}
                        >
                            <Text>
                                {invrecord.contents
                                    .reduce(
                                        (sum, item) =>
                                            sum +
                                            item.taxableAmount +
                                            item.gstamount,
                                        0
                                    )
                                    .toFixed(2)}
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Payment Details and Totals */}
                <View style={[styles.container, styles.paymentContainer]}>
                    <View style={styles.sectionContainer}>
                        <Text style={styles.sectionTitle}>Payment Details</Text>
                        <Text>
                            <Text style={styles.boldText}>Payment Mode: </Text>
                            {paymentMode()}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>Bank Details: </Text>
                            {invrecord.bankdetails || "N/A"}
                        </Text>
                    </View>

                    <View style={styles.verticalDivider} />

                    <View style={styles.sectionContainer}>
                        <Text>
                            <Text style={styles.boldText}>
                                Total Taxable Amount:{" "}
                            </Text>
                            {invrecord.contents
                                .reduce(
                                    (sum, item) => sum + item.taxableAmount,
                                    0
                                )
                                .toFixed(2)}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>
                                Total Discount:{" "}
                            </Text>
                            {invrecord.contents
                                .reduce(
                                    (sum, item) =>
                                        sum + (item.discountAmount || 0),
                                    0
                                )
                                .toFixed(2)}
                        </Text>
                        {invrecord.taxstate === invrecord.sourcestate ? (
                            <>
                                <Text>
                                    <Text style={styles.boldText}>
                                        Total CGST:{" "}
                                    </Text>
                                    {(
                                        invrecord.contents.reduce(
                                            (sum, item) => sum + item.gstamount,
                                            0
                                        ) / 2
                                    ).toFixed(2)}
                                </Text>
                                <Text>
                                    <Text style={styles.boldText}>
                                        Total SGST:{" "}
                                    </Text>
                                    {(
                                        invrecord.contents.reduce(
                                            (sum, item) => sum + item.gstamount,
                                            0
                                        ) / 2
                                    ).toFixed(2)}
                                </Text>
                            </>
                        ) : (
                            <Text>
                                <Text style={styles.boldText}>
                                    Total IGST:{" "}
                                </Text>
                                {invrecord.contents
                                    .reduce(
                                        (sum, item) => sum + item.gstamount,
                                        0
                                    )
                                    .toFixed(2)}
                            </Text>
                        )}
                        <Text>
                            <Text style={styles.boldText}>
                                Total Invoice Amount:{" "}
                            </Text>
                            {invrecord.invoicetotal}
                        </Text>
                        <Text>
                            <Text style={styles.boldText}>
                                Total Paid Amount:{" "}
                            </Text>
                            {invrecord.amountpaid}
                        </Text>
                    </View>
                </View>

                {/* Amount in words */}
                <View style={styles.container}>
                    <Text style={{ textAlign: "center" }}>
                        <Text style={styles.boldText}>Amount in words: </Text>
                        {(invrecord.invoicetotalword || "N/A")}                   
                         </Text>
                </View>

                {/* Transportation Details, Terms and Conditions, and Signature in one row */}
                {(() => {
                    // Check if there's any meaningful transportation data
                    const hasTransportationData =
                        invrecord.transportationmode &&
                        invrecord.transportationmode.toLowerCase() !== "none" &&
                        invrecord.transportationmode.toLowerCase() !== "n/a" &&
                        invrecord.transportationmode.trim() !== "";

                    // Check if terms and conditions exist
                    const hasTerms =
                        orgDetails?.bisdetails?.invoice_preferences?.tandc &&
                        orgDetails.bisdetails.invoice_preferences.tandc.trim() !==
                            "";

                    // Check if signature exists
                    const hasSignature =
                        orgDetails?.bisdetails?.invoice_preferences?.sign &&
                        orgDetails.bisdetails.invoice_preferences.sign.trim() !==
                            "";

                    // Check if signature is the only section (for right alignment)
                    const isSignatureOnly =
                        hasSignature && !hasTransportationData && !hasTerms;

                    // If we have transportation data, terms, or signature, render the appropriate sections
                    if (hasTransportationData || hasTerms || hasSignature) {
                        return (
                            <View
                                style={[
                                    styles.container,
                                    isSignatureOnly
                                        ? {
                                              flexDirection: "row",
                                              justifyContent: "flex-end",
                                              marginVertical: 12,
                                              gap: 24,
                                          }
                                        : styles.transportContainer,
                                ]}
                            >
                                {/* Transportation Details - only if data exists */}
                                {hasTransportationData && (
                                    <View style={styles.sectionContainer}>
                                        <Text style={styles.sectionTitle}>
                                            Transportation Details
                                        </Text>
                                        <Text>
                                            <Text style={styles.boldText}>
                                                Transportation Mode:{" "}
                                            </Text>
                                            {invrecord.transportationmode}
                                        </Text>
                                        {invrecord.vehicleno && (
                                            <Text>
                                                <Text style={styles.boldText}>
                                                    Vehicle No:{" "}
                                                </Text>
                                                {invrecord.vehicleno}
                                            </Text>
                                        )}
                                        {invrecord.dateofsupply && (
                                            <Text>
                                                <Text style={styles.boldText}>
                                                    Date of Supply:{" "}
                                                </Text>
                                                {formatDate(
                                                    invrecord.dateofsupply
                                                )}
                                            </Text>
                                        )}
                                    </View>
                                )}

                                {/* Add divider only if we have multiple sections */}
                                {hasTransportationData &&
                                    (invrecord?.inoutflag === 15 && (hasTerms || hasSignature)) && (
                                        <View style={styles.verticalDivider} />
                                    )}

                                {/* Terms and Conditions - only if they exist */}
                                { invrecord?.inoutflag === 15 &&hasTerms && (
                                    <View style={styles.sectionContainer}>
                                        <Text style={styles.sectionTitle}>
                                            Terms and Conditions
                                        </Text>
                                        <Text>
                                            {
                                                orgDetails?.bisdetails
                                                    ?.invoice_preferences?.tandc
                                            }
                                        </Text>
                                    </View>
                                )}

                                {/* Add divider between terms and signature if both exist */}
                                {hasTerms && hasSignature && (
                                    <View style={styles.verticalDivider} />
                                )}

                                {/* Signature - only if it exists */}
                                {invrecord?.inoutflag === 15 && hasSignature && (
                                    <View style={styles.sectionContainer}>
                                        {/* <Text style={styles.sectionTitle}>
                                            Signature
                                        </Text> */}
                                        <Text
                                            style={
                                                isSignatureOnly
                                                    ? {
                                                          ...styles.sectionTitle,
                                                          textAlign: "right",
                                                      }
                                                    : styles.sectionTitle
                                            }
                                        >
                                            Signature
                                        </Text>
                                        <View
                                            style={{
                                                alignItems: isSignatureOnly
                                                    ? "flex-end"
                                                    : "center",
                                            }}
                                        >
                                            {renderBase64Image(
                                                orgDetails?.bisdetails
                                                    ?.invoice_preferences?.sign,
                                                styles.signature,
                                                "" // Empty string for no fallback text
                                            )}
                                            <Text style={styles.footerText}>
                                                Authorized Signature
                                            </Text>
                                        </View>
                                    </View>
                                )}
                            </View>
                        );
                    }

                    // If none of the sections have data, don't render anything
                    return null;
                })()}

                {/* Footer */}
                <View style={styles.footer}>
                    <Text style={styles.footerText}>
                        Thank you for your business!
                    </Text>
                </View>
            </Page>
        </Document>
    );
};

export default InvoicePdf;
